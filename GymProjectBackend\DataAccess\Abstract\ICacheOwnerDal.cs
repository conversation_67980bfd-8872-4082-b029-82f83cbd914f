using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    /// <summary>
    /// Cache Owner Data Access Layer Interface
    /// Redis operasyonları için DAL katmanı
    /// Single Responsibility: Sadece cache data access işlemleri
    /// </summary>
    public interface ICacheOwnerDal
    {
        #region Cache Statistics Operations

        /// <summary>
        /// Şirket cache istatistiklerini hesaplar
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Cache istatistikleri</returns>
        Task<CacheStatisticsDto> GetCompanyCacheStatisticsAsync(int companyId);

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini hesaplar
        /// </summary>
        /// <param name="companyIds">Şirket ID listesi</param>
        /// <returns>Multi-company cache istatistikleri</returns>
        Task<List<CacheStatisticsDto>> GetMultipleCompaniesCacheStatisticsAsync(List<int> companyIds);

        #endregion

        #region Cache Key Operations

        /// <summary>
        /// Şirket cache key'lerini pattern ile getirir (pagination ile)
        /// </summary>
        /// <param name="pattern">Cache pattern</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <returns>Cache key listesi</returns>
        Task<CacheKeysResponseDto> GetKeysByPatternAsync(string pattern, int page, int size);

        /// <summary>
        /// Cache key'inin detaylarını getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Cache key detayı</returns>
        Task<CacheKeyDetailDto> GetCacheKeyDetailAsync(string key);

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Cache key değeri</returns>
        Task<CacheKeyValueDto> GetCacheKeyValueAsync(string key);

        /// <summary>
        /// En çok kullanılan cache key'lerini getirir
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="count">Getirilecek key sayısı</param>
        /// <returns>Top cache key'leri</returns>
        Task<List<CacheKeyDetailDto>> GetTopCacheKeysAsync(int companyId, int count);

        #endregion

        #region Cache Clear Operations

        /// <summary>
        /// Pattern'e göre cache key'lerini siler
        /// </summary>
        /// <param name="pattern">Silinecek pattern</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> RemoveByPatternAsync(string pattern);

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        /// <param name="key">Silinecek key</param>
        /// <returns>Silme başarılı mı</returns>
        Task<bool> RemoveKeyAsync(string key);

        /// <summary>
        /// Çoklu pattern'e göre cache temizleme
        /// </summary>
        /// <param name="patterns">Silinecek pattern'ler</param>
        /// <returns>Toplam silinen key sayısı</returns>
        Task<long> RemoveMultiplePatternsAsync(List<string> patterns);

        #endregion

        #region Cache Health Operations

        /// <summary>
        /// Redis bağlantı durumunu kontrol eder
        /// </summary>
        /// <returns>Cache sağlık durumu</returns>
        Task<CacheHealthDto> GetCacheHealthAsync();

        /// <summary>
        /// Redis server bilgilerini getirir
        /// </summary>
        /// <returns>Server bilgileri</returns>
        Task<object> GetRedisServerInfoAsync();

        /// <summary>
        /// Cache performance metrics getirir
        /// </summary>
        /// <returns>Performance metrics</returns>
        Task<CachePerformanceDto> GetCachePerformanceAsync();

        #endregion

        #region Cache Warmup Operations

        /// <summary>
        /// Belirli entity'ler için cache warmup yapar
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entities">Warmup yapılacak entity'ler</param>
        /// <returns>Warmup sonucu</returns>
        Task<CacheWarmupResultDto> PerformCacheWarmupAsync(int companyId, List<string> entities);

        #endregion

        #region Utility Operations

        /// <summary>
        /// Cache key'inin şirkete ait olup olmadığını kontrol eder
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Şirkete ait mi</returns>
        bool IsKeyBelongsToCompany(string key, int companyId);

        /// <summary>
        /// Şirket cache pattern'ini oluşturur
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entity">Entity adı (opsiyonel)</param>
        /// <returns>Cache pattern</returns>
        string BuildCompanyCachePattern(int companyId, string entity = null);

        /// <summary>
        /// Cache key'inden entity adını çıkarır
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Entity adı</returns>
        string ExtractEntityFromKey(string key);

        /// <summary>
        /// Cache key'inden action adını çıkarır
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Action adı</returns>
        string ExtractActionFromKey(string key);

        #endregion
    }
}
