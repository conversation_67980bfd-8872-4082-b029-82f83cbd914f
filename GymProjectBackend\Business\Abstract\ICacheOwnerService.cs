using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Business.Abstract
{
    /// <summary>
    /// Cache Owner Service Interface - SOLID prensiplerine uygun cache yönetimi
    /// Sadece Owner rolü erişebilir - SecuredOperation("owner") ile korunur
    /// Single Responsibility: Sadece cache owner işlemleri
    /// Interface Segregation: Sadece gerekli cache owner operasyonları
    /// </summary>
    public interface ICacheOwnerService
    {
        #region Company Cache Operations

        /// <summary>
        /// Şirket cache istatistiklerini getirir
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache istatistikleri</returns>
        Task<IDataResult<CacheStatisticsDto>> GetCacheStatisticsAsync(int? companyId = null);

        /// <summary>
        /// Şirket cache detaylarını getirir
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache detayları</returns>
        Task<IDataResult<CacheDetailsDto>> GetCacheDetailsAsync(int? companyId = null);

        /// <summary>
        /// Şirket cache'ini temizler
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearCompanyCacheAsync(int? companyId = null);

        #endregion

        #region Cache Key Operations

        /// <summary>
        /// Şirket cache key'lerini listeler (pagination ile)
        /// </summary>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache key listesi</returns>
        Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysAsync(int page = 1, int size = 50, int? companyId = null);

        /// <summary>
        /// Pattern'e göre cache key'lerini listeler
        /// </summary>
        /// <param name="pattern">Arama pattern'i</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache key listesi</returns>
        Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysByPatternAsync(string pattern, int page = 1, int size = 50, int? companyId = null);

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Silme sonucu</returns>
        Task<IDataResult<CacheKeyDeleteResultDto>> DeleteCacheKeyAsync(string key);

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Cache değeri</returns>
        Task<IDataResult<CacheKeyValueDto>> GetCacheKeyValueAsync(string key);

        #endregion

        #region Pattern Operations

        /// <summary>
        /// Pattern'e göre cache'leri temizler
        /// </summary>
        /// <param name="pattern">Temizlenecek pattern</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearCacheByPatternAsync(string pattern, int? companyId = null);

        #endregion

        #region Health & Monitoring

        /// <summary>
        /// Cache sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Sağlık durumu</returns>
        Task<IDataResult<CacheHealthDto>> GetCacheHealthAsync();

        /// <summary>
        /// Real-time cache metrics getirir
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Real-time metrics</returns>
        Task<IDataResult<CacheRealtimeMetricsDto>> GetRealtimeMetricsAsync(int? companyId = null);

        #endregion

        #region Cache Warmup

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        /// <param name="request">Warmup parametreleri</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Warmup sonucu</returns>
        Task<IDataResult<CacheWarmupResultDto>> WarmupCacheAsync(CacheWarmupRequestDto request, int? companyId = null);

        #endregion

        #region Multi-Company Operations (Owner Only)

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini getirir (Owner only)
        /// </summary>
        /// <returns>Tüm şirket cache istatistikleri</returns>
        Task<IDataResult<MultiCompanyCacheStatisticsDto>> GetAllCompaniesStatisticsAsync();

        /// <summary>
        /// Belirli bir şirketin cache detaylarını getirir (Owner only)
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Şirket cache detayları</returns>
        Task<IDataResult<CompanyCacheDetailsDto>> GetSpecificCompanyCacheDetailsAsync(int companyId);

        /// <summary>
        /// Belirli bir şirketin cache'ini temizler (Owner only)
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearSpecificCompanyCacheAsync(int companyId);

        /// <summary>
        /// Çoklu şirket cache temizleme (Owner only)
        /// </summary>
        /// <param name="request">Toplu temizleme parametreleri</param>
        /// <returns>Toplu temizleme sonucu</returns>
        Task<IDataResult<BulkCacheClearResultDto>> BulkClearCompaniesCache(BulkCacheOperationRequestDto request);

        #endregion
    }
}
