using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Business.Concrete
{
    /// <summary>
    /// Cache Owner Manager - SOLID prensiplerine uygun cache yönetimi
    /// Single Responsibility: Sadece cache owner business logic
    /// Open/Closed: Extension için açık, modification için kapalı
    /// Liskov Substitution: ICacheOwnerService interface'ini tam implement eder
    /// Interface Segregation: Sadece gerekli cache owner operasyonları
    /// Dependency Inversion: Abstraction'lara bağımlı, concrete'lere değil
    /// </summary>
    public class CacheOwnerManager : ICacheOwnerService
    {
        private readonly ICacheOwnerDal _cacheOwnerDal;
        private readonly ICompanyContext _companyContext;
        private readonly ICompanyService _companyService;

        public CacheOwnerManager(ICacheOwnerDal cacheOwnerDal, ICompanyContext companyContext, ICompanyService companyService)
        {
            _cacheOwnerDal = cacheOwnerDal;
            _companyContext = companyContext;
            _companyService = companyService;
        }

        #region Company Cache Operations

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheStatisticsDto>> GetCacheStatisticsAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheStatisticsDto>("Geçersiz company ID");

                var statistics = await _cacheOwnerDal.GetCompanyCacheStatisticsAsync(targetCompanyId);
                return new SuccessDataResult<CacheStatisticsDto>(statistics, "Cache istatistikleri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheStatisticsDto>($"Cache istatistikleri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheDetailsDto>> GetCacheDetailsAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheDetailsDto>("Geçersiz company ID");

                var statistics = await _cacheOwnerDal.GetCompanyCacheStatisticsAsync(targetCompanyId);
                var health = await _cacheOwnerDal.GetCacheHealthAsync();

                var details = new CacheDetailsDto
                {
                    Statistics = statistics,
                    Health = health,
                    CompanyId = targetCompanyId,
                    CachePatterns = new[]
                    {
                        $"gym:{targetCompanyId}:member:*",
                        $"gym:{targetCompanyId}:payment:*",
                        $"gym:{targetCompanyId}:membership:*",
                        $"gym:{targetCompanyId}:user:*",
                        $"gym:{targetCompanyId}:company:*"
                    }
                };

                return new SuccessDataResult<CacheDetailsDto>(details, "Cache detayları başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheDetailsDto>($"Cache detayları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheClearResultDto>> ClearCompanyCacheAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheClearResultDto>("Geçersiz company ID");

                var pattern = _cacheOwnerDal.BuildCompanyCachePattern(targetCompanyId);
                var removedCount = await _cacheOwnerDal.RemoveByPatternAsync(pattern);

                var companyResult = _companyService.GetById(targetCompanyId);
                var companyName = companyResult.Success ? companyResult.Data.CompanyName : "Bilinmeyen Şirket";

                var result = new CacheClearResultDto
                {
                    RemovedCount = removedCount,
                    Pattern = pattern,
                    CompanyId = targetCompanyId,
                    CompanyName = companyName,
                    ClearedAt = DateTime.UtcNow
                };

                return new SuccessDataResult<CacheClearResultDto>(result, 
                    $"Company cache'i başarıyla temizlendi. {removedCount} adet key silindi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheClearResultDto>($"Company cache temizlenirken hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Cache Key Operations

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysAsync(int page = 1, int size = 50, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheKeysResponseDto>("Geçersiz company ID");

                var pattern = _cacheOwnerDal.BuildCompanyCachePattern(targetCompanyId);
                var keys = await _cacheOwnerDal.GetKeysByPatternAsync(pattern, page, size);

                return new SuccessDataResult<CacheKeysResponseDto>(keys, "Cache key'leri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheKeysResponseDto>($"Cache key'leri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysByPatternAsync(string pattern, int page = 1, int size = 50, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheKeysResponseDto>("Geçersiz company ID");

                // Security: Company ID kontrolü
                if (!pattern.StartsWith($"gym:{targetCompanyId}:"))
                {
                    pattern = $"gym:{targetCompanyId}:{pattern.TrimStart('*')}";
                }

                var keys = await _cacheOwnerDal.GetKeysByPatternAsync(pattern, page, size);
                return new SuccessDataResult<CacheKeysResponseDto>(keys, "Cache key'leri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheKeysResponseDto>($"Cache key'leri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeyDeleteResultDto>> DeleteCacheKeyAsync(string key)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                
                if (companyId <= 0)
                    return new ErrorDataResult<CacheKeyDeleteResultDto>("Geçersiz company ID");

                // Security: Company ID kontrolü
                if (!_cacheOwnerDal.IsKeyBelongsToCompany(key, companyId))
                {
                    return new ErrorDataResult<CacheKeyDeleteResultDto>("Bu cache key'ine erişim yetkiniz yok");
                }

                var removed = await _cacheOwnerDal.RemoveKeyAsync(key);
                
                var result = new CacheKeyDeleteResultDto
                {
                    Key = key,
                    Removed = removed,
                    DeletedAt = DateTime.UtcNow
                };

                return new SuccessDataResult<CacheKeyDeleteResultDto>(result,
                    removed ? "Cache key başarıyla silindi" : "Cache key bulunamadı");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheKeyDeleteResultDto>($"Cache key silinirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeyValueDto>> GetCacheKeyValueAsync(string key)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                
                if (companyId <= 0)
                    return new ErrorDataResult<CacheKeyValueDto>("Geçersiz company ID");

                // Security: Company ID kontrolü
                if (!_cacheOwnerDal.IsKeyBelongsToCompany(key, companyId))
                {
                    return new ErrorDataResult<CacheKeyValueDto>("Bu cache key'ine erişim yetkiniz yok");
                }

                var value = await _cacheOwnerDal.GetCacheKeyValueAsync(key);
                return new SuccessDataResult<CacheKeyValueDto>(value, "Cache key değeri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheKeyValueDto>($"Cache key değeri alınırken hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Pattern Operations

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheClearResultDto>> ClearCacheByPatternAsync(string pattern, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheClearResultDto>("Geçersiz company ID");

                // Security: Company ID kontrolü
                if (!pattern.StartsWith($"gym:{targetCompanyId}:"))
                {
                    pattern = $"gym:{targetCompanyId}:{pattern.TrimStart('*')}";
                }

                var removedCount = await _cacheOwnerDal.RemoveByPatternAsync(pattern);

                var companyResult = _companyService.GetById(targetCompanyId);
                var companyName = companyResult.Success ? companyResult.Data.CompanyName : "Bilinmeyen Şirket";

                var result = new CacheClearResultDto
                {
                    RemovedCount = removedCount,
                    Pattern = pattern,
                    CompanyId = targetCompanyId,
                    CompanyName = companyName,
                    ClearedAt = DateTime.UtcNow
                };

                return new SuccessDataResult<CacheClearResultDto>(result,
                    $"Pattern cache'i başarıyla temizlendi. {removedCount} adet key silindi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheClearResultDto>($"Pattern cache temizlenirken hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Health & Monitoring

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheHealthDto>> GetCacheHealthAsync()
        {
            try
            {
                var health = await _cacheOwnerDal.GetCacheHealthAsync();
                return new SuccessDataResult<CacheHealthDto>(health, "Cache sağlık durumu başarıyla kontrol edildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheHealthDto>($"Cache sağlık durumu kontrol edilirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheRealtimeMetricsDto>> GetRealtimeMetricsAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheRealtimeMetricsDto>("Geçersiz company ID");

                var statistics = await _cacheOwnerDal.GetCompanyCacheStatisticsAsync(targetCompanyId);
                var performance = await _cacheOwnerDal.GetCachePerformanceAsync();
                var serverInfo = await _cacheOwnerDal.GetRedisServerInfoAsync();
                var topKeys = await _cacheOwnerDal.GetTopCacheKeysAsync(targetCompanyId, 10);

                var metrics = new CacheRealtimeMetricsDto
                {
                    CompanyId = targetCompanyId,
                    Timestamp = DateTime.UtcNow,
                    Statistics = statistics,
                    Performance = performance,
                    ServerInfo = serverInfo,
                    TopCacheKeys = topKeys
                };

                return new SuccessDataResult<CacheRealtimeMetricsDto>(metrics, "Real-time metrics başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheRealtimeMetricsDto>($"Real-time metrics alınırken hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Cache Warmup

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheWarmupResultDto>> WarmupCacheAsync(CacheWarmupRequestDto request, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();

                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheWarmupResultDto>("Geçersiz company ID");

                var entities = new List<string>();

                if (request.WarmupMembers) entities.Add("member");
                if (request.WarmupPayments) entities.Add("payment");
                if (request.WarmupMemberships) entities.Add("membership");
                if (request.WarmupUsers) entities.Add("user");
                if (request.WarmupCompanySettings) entities.Add("company");

                var result = await _cacheOwnerDal.PerformCacheWarmupAsync(targetCompanyId, entities);
                return new SuccessDataResult<CacheWarmupResultDto>(result, "Cache warmup işlemi başarıyla tamamlandı");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheWarmupResultDto>($"Cache warmup işlemi sırasında hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Multi-Company Operations (Owner Only)

        [SecuredOperation("owner")]
        public async Task<IDataResult<MultiCompanyCacheStatisticsDto>> GetAllCompaniesStatisticsAsync()
        {
            try
            {
                var companiesResult = _companyService.GetAll();
                if (!companiesResult.Success)
                    return new ErrorDataResult<MultiCompanyCacheStatisticsDto>("Şirketler alınamadı");

                var companies = companiesResult.Data.Where(c => c.IsActive == true).ToList();
                var companyIds = companies.Select(c => c.CompanyID).ToList();

                var allStats = await _cacheOwnerDal.GetMultipleCompaniesCacheStatisticsAsync(companyIds);

                var companyCacheStats = companies.Select(company =>
                {
                    var stats = allStats.FirstOrDefault(s => s.CompanyId == company.CompanyID);
                    return new CompanyCacheStatisticsDto
                    {
                        CompanyId = company.CompanyID,
                        CompanyName = company.CompanyName,
                        Statistics = stats ?? new CacheStatisticsDto { CompanyId = company.CompanyID },
                        IsActive = company.IsActive,
                        CreationDate = company.CreationDate
                    };
                }).ToList();

                var totalStats = new MultiCompanyCacheStatisticsDto
                {
                    TotalCompanies = companies.Count,
                    TotalCacheKeys = allStats.Sum(s => s.TotalKeys),
                    TotalMemoryUsage = allStats.Sum(s => s.TotalMemoryUsage),
                    AverageKeysPerCompany = allStats.Any() ? allStats.Average(s => s.TotalKeys) : 0,
                    Companies = companyCacheStats
                };

                return new SuccessDataResult<MultiCompanyCacheStatisticsDto>(totalStats,
                    "Tüm şirket cache istatistikleri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MultiCompanyCacheStatisticsDto>($"Cache istatistikleri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CompanyCacheDetailsDto>> GetSpecificCompanyCacheDetailsAsync(int companyId)
        {
            try
            {
                var companyResult = _companyService.GetById(companyId);
                if (!companyResult.Success)
                    return new ErrorDataResult<CompanyCacheDetailsDto>("Şirket bulunamadı");

                var cacheDetails = await GetCacheDetailsAsync(companyId);
                if (!cacheDetails.Success)
                    return new ErrorDataResult<CompanyCacheDetailsDto>(cacheDetails.Message);

                var result = new CompanyCacheDetailsDto
                {
                    Company = new CompanyDto
                    {
                        CompanyID = companyResult.Data.CompanyID,
                        CompanyName = companyResult.Data.CompanyName,
                        IsActive = companyResult.Data.IsActive,
                        CreationDate = companyResult.Data.CreationDate
                    },
                    CacheDetails = cacheDetails.Data
                };

                return new SuccessDataResult<CompanyCacheDetailsDto>(result, "Şirket cache detayları başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CompanyCacheDetailsDto>($"Şirket cache detayları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheClearResultDto>> ClearSpecificCompanyCacheAsync(int companyId)
        {
            try
            {
                var companyResult = _companyService.GetById(companyId);
                if (!companyResult.Success)
                    return new ErrorDataResult<CacheClearResultDto>("Şirket bulunamadı");

                var clearResult = await ClearCompanyCacheAsync(companyId);
                return clearResult;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheClearResultDto>($"Şirket cache'i temizlenirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<BulkCacheClearResultDto>> BulkClearCompaniesCache(BulkCacheOperationRequestDto request)
        {
            try
            {
                var results = new List<BulkCacheOperationResultDto>();
                var totalRemovedCount = 0L;

                foreach (var companyId in request.CompanyIds)
                {
                    try
                    {
                        var companyResult = _companyService.GetById(companyId);
                        var companyName = companyResult.Success ? companyResult.Data.CompanyName : $"Company_{companyId}";

                        var clearResult = await ClearCompanyCacheAsync(companyId);

                        if (clearResult.Success)
                        {
                            totalRemovedCount += clearResult.Data.RemovedCount;
                            results.Add(new BulkCacheOperationResultDto
                            {
                                CompanyId = companyId,
                                CompanyName = companyName,
                                RemovedCount = clearResult.Data.RemovedCount,
                                Success = true
                            });
                        }
                        else
                        {
                            results.Add(new BulkCacheOperationResultDto
                            {
                                CompanyId = companyId,
                                CompanyName = companyName,
                                RemovedCount = 0,
                                Success = false,
                                ErrorMessage = clearResult.Message
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        results.Add(new BulkCacheOperationResultDto
                        {
                            CompanyId = companyId,
                            CompanyName = $"Company_{companyId}",
                            RemovedCount = 0,
                            Success = false,
                            ErrorMessage = ex.Message
                        });
                    }
                }

                var bulkResult = new BulkCacheClearResultDto
                {
                    TotalRemovedCount = totalRemovedCount,
                    ProcessedCompanies = request.CompanyIds.Count,
                    Results = results
                };

                return new SuccessDataResult<BulkCacheClearResultDto>(bulkResult,
                    $"Toplu cache temizleme tamamlandı. Toplam {totalRemovedCount} adet key silindi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<BulkCacheClearResultDto>($"Toplu cache temizleme sırasında hata oluştu: {ex.Message}");
            }
        }

        #endregion
    }
