using Core.Utilities.Results;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Business.Abstract
{
    /// <summary>
    /// Cache Admin Service Interface - SOLID prensiplerine uygun cache yönetimi
    /// Single Responsibility: Sadece cache admin işlemleri
    /// Interface Segregation: <PERSON><PERSON><PERSON> gerek<PERSON> cache admin operasyonları
    /// </summary>
    public interface ICacheAdminService
    {
        #region Company Cache Operations

        /// <summary>
        /// Şirket cache istatistiklerini getirir
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache istatistikleri</returns>
        Task<IDataResult<CacheStatisticsDto>> GetCacheStatisticsAsync(int? companyId = null);

        /// <summary>
        /// Şirket cache detaylarını getirir
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache detayları</returns>
        Task<IDataResult<CacheDetailsDto>> GetCacheDetailsAsync(int? companyId = null);

        /// <summary>
        /// Şirket cache'ini temizler
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearCompanyCacheAsync(int? companyId = null);

        #endregion

        #region Cache Key Operations

        /// <summary>
        /// Şirket cache key'lerini listeler (pagination ile)
        /// </summary>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache key listesi</returns>
        Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysAsync(int page = 1, int size = 50, int? companyId = null);

        /// <summary>
        /// Pattern'e göre cache key'lerini listeler
        /// </summary>
        /// <param name="pattern">Arama pattern'i</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Cache key listesi</returns>
        Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysByPatternAsync(string pattern, int page = 1, int size = 50, int? companyId = null);

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Silme sonucu</returns>
        Task<IDataResult<CacheKeyDeleteResultDto>> DeleteCacheKeyAsync(string key);

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Cache değeri</returns>
        Task<IDataResult<CacheKeyValueDto>> GetCacheKeyValueAsync(string key);

        #endregion

        #region Pattern Operations

        /// <summary>
        /// Pattern'e göre cache'leri temizler
        /// </summary>
        /// <param name="pattern">Temizlenecek pattern</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearCacheByPatternAsync(string pattern, int? companyId = null);

        #endregion

        #region Health & Monitoring

        /// <summary>
        /// Cache sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Sağlık durumu</returns>
        Task<IDataResult<CacheHealthDto>> GetCacheHealthAsync();

        /// <summary>
        /// Real-time cache metrics getirir
        /// </summary>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Real-time metrics</returns>
        Task<IDataResult<CacheRealtimeMetricsDto>> GetRealtimeMetricsAsync(int? companyId = null);

        #endregion

        #region Cache Warmup

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        /// <param name="request">Warmup parametreleri</param>
        /// <param name="companyId">Şirket ID (opsiyonel, null ise current company)</param>
        /// <returns>Warmup sonucu</returns>
        Task<IDataResult<CacheWarmupResultDto>> WarmupCacheAsync(CacheWarmupRequestDto request, int? companyId = null);

        #endregion
    }

    #region DTOs

    /// <summary>
    /// Cache istatistikleri DTO
    /// </summary>
    public class CacheStatisticsDto
    {
        public int CompanyId { get; set; }
        public int TotalKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public double TotalMemoryUsageMB { get; set; }
        public Dictionary<string, int> KeysByEntity { get; set; }
        public DateTime LastUpdated { get; set; }
        public List<CacheKeyDetailDto> TopKeys { get; set; }
    }

    /// <summary>
    /// Cache detayları DTO
    /// </summary>
    public class CacheDetailsDto
    {
        public CacheStatisticsDto Statistics { get; set; }
        public CacheHealthDto Health { get; set; }
        public int CompanyId { get; set; }
        public string[] CachePatterns { get; set; }
    }

    /// <summary>
    /// Cache temizleme sonucu DTO
    /// </summary>
    public class CacheClearResultDto
    {
        public long RemovedCount { get; set; }
        public string Pattern { get; set; }
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public DateTime ClearedAt { get; set; }
    }

    /// <summary>
    /// Cache key listesi DTO
    /// </summary>
    public class CacheKeysResponseDto
    {
        public List<CacheKeyDetailDto> Keys { get; set; }
        public CachePaginationDto Pagination { get; set; }
        public string Pattern { get; set; }
    }

    /// <summary>
    /// Cache key detayı DTO
    /// </summary>
    public class CacheKeyDetailDto
    {
        public string Key { get; set; }
        public string Type { get; set; }
        public long MemoryUsage { get; set; }
        public DateTime? ExpiryTime { get; set; }
        public TimeSpan? TimeToLive { get; set; }
        public string Entity { get; set; }
        public string Action { get; set; }
    }

    /// <summary>
    /// Cache pagination DTO
    /// </summary>
    public class CachePaginationDto
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    /// <summary>
    /// Cache key silme sonucu DTO
    /// </summary>
    public class CacheKeyDeleteResultDto
    {
        public string Key { get; set; }
        public bool Removed { get; set; }
        public DateTime DeletedAt { get; set; }
    }

    /// <summary>
    /// Cache key değeri DTO
    /// </summary>
    public class CacheKeyValueDto
    {
        public string Key { get; set; }
        public object Value { get; set; }
        public string Type { get; set; }
        public long MemoryUsage { get; set; }
        public DateTime? ExpiryTime { get; set; }
    }

    /// <summary>
    /// Cache sağlık durumu DTO
    /// </summary>
    public class CacheHealthDto
    {
        public bool IsConnected { get; set; }
        public double PingTime { get; set; }
        public double ResponseTime { get; set; }
        public string Status { get; set; }
        public object ServerInfo { get; set; }
    }

    /// <summary>
    /// Real-time cache metrics DTO
    /// </summary>
    public class CacheRealtimeMetricsDto
    {
        public int CompanyId { get; set; }
        public DateTime Timestamp { get; set; }
        public CacheStatisticsDto Statistics { get; set; }
        public CachePerformanceDto Performance { get; set; }
        public object ServerInfo { get; set; }
        public List<CacheKeyDetailDto> TopCacheKeys { get; set; }
    }

    /// <summary>
    /// Cache performance DTO
    /// </summary>
    public class CachePerformanceDto
    {
        public double ResponseTime { get; set; }
        public bool IsConnected { get; set; }
        public int ConnectionCount { get; set; }
    }

    /// <summary>
    /// Cache warmup request DTO
    /// </summary>
    public class CacheWarmupRequestDto
    {
        public bool WarmupMembers { get; set; } = true;
        public bool WarmupPayments { get; set; } = true;
        public bool WarmupMemberships { get; set; } = true;
        public bool WarmupUsers { get; set; } = false;
        public bool WarmupCompanySettings { get; set; } = false;
    }

    /// <summary>
    /// Cache warmup sonucu DTO
    /// </summary>
    public class CacheWarmupResultDto
    {
        public int CompanyId { get; set; }
        public int TotalWarmedKeys { get; set; }
        public Dictionary<string, int> WarmedKeysByEntity { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime CompletedAt { get; set; }
        public List<string> Errors { get; set; }
    }

    #endregion
}
