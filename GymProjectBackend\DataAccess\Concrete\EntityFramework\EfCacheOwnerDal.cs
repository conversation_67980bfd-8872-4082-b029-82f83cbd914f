using DataAccess.Abstract;
using Entities.DTOs;
using Core.CrossCuttingConcerns.Caching;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// Cache Owner Data Access Layer Implementation
    /// Redis operasyonları için concrete implementation
    /// Dependency Inversion: ICacheOwnerDal interface'ini implement eder
    /// </summary>
    public class EfCacheOwnerDal : ICacheOwnerDal
    {
        private readonly ICacheService _cacheService;
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        private readonly IDatabase _database;

        public EfCacheOwnerDal(ICacheService cacheService, IConnectionMultiplexer connectionMultiplexer)
        {
            _cacheService = cacheService;
            _connectionMultiplexer = connectionMultiplexer;
            _database = _connectionMultiplexer.GetDatabase();
        }

        #region Cache Statistics Operations

        public async Task<CacheStatisticsDto> GetCompanyCacheStatisticsAsync(int companyId)
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var pattern = BuildCompanyCachePattern(companyId);
            var keys = server.Keys(pattern: pattern).ToList();

            var totalKeys = keys.Count;
            var totalMemoryUsage = 0L;
            var keysByEntity = new Dictionary<string, int>();

            foreach (var key in keys)
            {
                try
                {
                    var memoryUsage = await server.MemoryUsageAsync(key);
                    totalMemoryUsage += memoryUsage ?? 0;

                    var entity = ExtractEntityFromKey(key);
                    if (!string.IsNullOrEmpty(entity))
                    {
                        keysByEntity[entity] = keysByEntity.GetValueOrDefault(entity, 0) + 1;
                    }
                }
                catch
                {
                    // Key silinmiş olabilir, devam et
                }
            }

            var topKeys = await GetTopCacheKeysAsync(companyId, 10);

            return new CacheStatisticsDto
            {
                CompanyId = companyId,
                TotalKeys = totalKeys,
                TotalMemoryUsage = totalMemoryUsage,
                TotalMemoryUsageMB = Math.Round(totalMemoryUsage / (1024.0 * 1024.0), 2),
                KeysByEntity = keysByEntity,
                LastUpdated = DateTime.UtcNow,
                TopKeys = topKeys
            };
        }

        public async Task<List<CacheStatisticsDto>> GetMultipleCompaniesCacheStatisticsAsync(List<int> companyIds)
        {
            var results = new List<CacheStatisticsDto>();

            foreach (var companyId in companyIds)
            {
                try
                {
                    var stats = await GetCompanyCacheStatisticsAsync(companyId);
                    results.Add(stats);
                }
                catch
                {
                    // Hatalı company için boş stats ekle
                    results.Add(new CacheStatisticsDto
                    {
                        CompanyId = companyId,
                        TotalKeys = 0,
                        TotalMemoryUsage = 0,
                        TotalMemoryUsageMB = 0,
                        KeysByEntity = new Dictionary<string, int>(),
                        LastUpdated = DateTime.UtcNow,
                        TopKeys = new List<CacheKeyDetailDto>()
                    });
                }
            }

            return results;
        }

        #endregion

        #region Cache Key Operations

        public async Task<CacheKeysResponseDto> GetKeysByPatternAsync(string pattern, int page, int size)
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var allKeys = server.Keys(pattern: pattern).ToList();

            var totalCount = allKeys.Count;
            var totalPages = (int)Math.Ceiling((double)totalCount / size);
            var skip = (page - 1) * size;
            var pagedKeys = allKeys.Skip(skip).Take(size);

            var keyDetails = new List<CacheKeyDetailDto>();

            foreach (var key in pagedKeys)
            {
                try
                {
                    var detail = await GetCacheKeyDetailAsync(key);
                    keyDetails.Add(detail);
                }
                catch
                {
                    // Key silinmiş olabilir, devam et
                }
            }

            return new CacheKeysResponseDto
            {
                Keys = keyDetails,
                Pagination = new CachePaginationDto
                {
                    CurrentPage = page,
                    PageSize = size,
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                    HasNextPage = page < totalPages,
                    HasPreviousPage = page > 1
                },
                Pattern = pattern
            };
        }

        public async Task<CacheKeyDetailDto> GetCacheKeyDetailAsync(string key)
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            
            var type = await _database.KeyTypeAsync(key);
            var memoryUsage = await server.MemoryUsageAsync(key) ?? 0;
            var ttl = await _database.KeyTimeToLiveAsync(key);
            
            DateTime? expiryTime = null;
            if (ttl.HasValue && ttl.Value.TotalSeconds > 0)
            {
                expiryTime = DateTime.UtcNow.Add(ttl.Value);
            }

            return new CacheKeyDetailDto
            {
                Key = key,
                Type = type.ToString(),
                MemoryUsage = memoryUsage,
                ExpiryTime = expiryTime,
                TimeToLive = ttl,
                Entity = ExtractEntityFromKey(key),
                Action = ExtractActionFromKey(key)
            };
        }

        public async Task<CacheKeyValueDto> GetCacheKeyValueAsync(string key)
        {
            var detail = await GetCacheKeyDetailAsync(key);
            var value = await _database.StringGetAsync(key);

            return new CacheKeyValueDto
            {
                Key = key,
                Value = value.HasValue ? value.ToString() : null,
                Type = detail.Type,
                MemoryUsage = detail.MemoryUsage,
                ExpiryTime = detail.ExpiryTime
            };
        }

        public async Task<List<CacheKeyDetailDto>> GetTopCacheKeysAsync(int companyId, int count)
        {
            var pattern = BuildCompanyCachePattern(companyId);
            var response = await GetKeysByPatternAsync(pattern, 1, count * 2); // Biraz fazla al
            
            return response.Keys
                .OrderByDescending(k => k.MemoryUsage)
                .Take(count)
                .ToList();
        }

        #endregion

        #region Cache Clear Operations

        public async Task<long> RemoveByPatternAsync(string pattern)
        {
            return await Task.Run(() => _cacheService.RemoveByPattern(pattern));
        }

        public async Task<bool> RemoveKeyAsync(string key)
        {
            return await _database.KeyDeleteAsync(key);
        }

        public async Task<long> RemoveMultiplePatternsAsync(List<string> patterns)
        {
            long totalRemoved = 0;
            
            foreach (var pattern in patterns)
            {
                var removed = await RemoveByPatternAsync(pattern);
                totalRemoved += removed;
            }

            return totalRemoved;
        }

        #endregion

        #region Cache Health Operations

        public async Task<CacheHealthDto> GetCacheHealthAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var pingTime = await _database.PingAsync();
            stopwatch.Stop();

            var serverInfo = await GetRedisServerInfoAsync();

            return new CacheHealthDto
            {
                IsConnected = _connectionMultiplexer.IsConnected,
                PingTime = pingTime.TotalMilliseconds,
                ResponseTime = stopwatch.ElapsedMilliseconds,
                Status = _connectionMultiplexer.IsConnected ? "Healthy" : "Unhealthy",
                ServerInfo = serverInfo
            };
        }

        public async Task<object> GetRedisServerInfoAsync()
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var info = await server.InfoAsync();
                
                return new
                {
                    Version = info.FirstOrDefault(x => x.Key == "redis_version")?.Value,
                    UptimeInSeconds = info.FirstOrDefault(x => x.Key == "uptime_in_seconds")?.Value,
                    ConnectedClients = info.FirstOrDefault(x => x.Key == "connected_clients")?.Value,
                    UsedMemory = info.FirstOrDefault(x => x.Key == "used_memory_human")?.Value,
                    TotalSystemMemory = info.FirstOrDefault(x => x.Key == "total_system_memory_human")?.Value
                };
            }
            catch
            {
                return new { Error = "Server bilgileri alınamadı" };
            }
        }

        public async Task<CachePerformanceDto> GetCachePerformanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            await _database.PingAsync();
            stopwatch.Stop();

            return new CachePerformanceDto
            {
                ResponseTime = stopwatch.ElapsedMilliseconds,
                IsConnected = _connectionMultiplexer.IsConnected,
                ConnectionCount = _connectionMultiplexer.GetCounters().Interactive.SocketCount
            };
        }

        #endregion

        #region Cache Warmup Operations

        public async Task<CacheWarmupResultDto> PerformCacheWarmupAsync(int companyId, List<string> entities)
        {
            var startTime = DateTime.UtcNow;
            var warmedKeys = new Dictionary<string, int>();
            var errors = new List<string>();
            var totalWarmed = 0;

            foreach (var entity in entities)
            {
                try
                {
                    // Bu kısım entity'ye göre özelleştirilmeli
                    // Şimdilik placeholder
                    var entityWarmedCount = await WarmupEntityCacheAsync(companyId, entity);
                    warmedKeys[entity] = entityWarmedCount;
                    totalWarmed += entityWarmedCount;
                }
                catch (Exception ex)
                {
                    errors.Add($"{entity}: {ex.Message}");
                }
            }

            var endTime = DateTime.UtcNow;

            return new CacheWarmupResultDto
            {
                CompanyId = companyId,
                TotalWarmedKeys = totalWarmed,
                WarmedKeysByEntity = warmedKeys,
                Duration = endTime - startTime,
                CompletedAt = endTime,
                Errors = errors
            };
        }

        private async Task<int> WarmupEntityCacheAsync(int companyId, string entity)
        {
            // Bu metot entity'ye göre özelleştirilmeli
            // Şimdilik placeholder
            await Task.Delay(100); // Simulated work
            return 0;
        }

        #endregion

        #region Utility Operations

        public bool IsKeyBelongsToCompany(string key, int companyId)
        {
            return key.StartsWith($"gym:{companyId}:");
        }

        public string BuildCompanyCachePattern(int companyId, string entity = null)
        {
            if (string.IsNullOrEmpty(entity))
            {
                return $"gym:{companyId}:*";
            }
            return $"gym:{companyId}:{entity}:*";
        }

        public string ExtractEntityFromKey(string key)
        {
            try
            {
                var parts = key.Split(':');
                if (parts.Length >= 3 && parts[0] == "gym")
                {
                    return parts[2]; // gym:companyId:entity:...
                }
            }
            catch { }
            
            return "unknown";
        }

        public string ExtractActionFromKey(string key)
        {
            try
            {
                var parts = key.Split(':');
                if (parts.Length >= 4 && parts[0] == "gym")
                {
                    return parts[3]; // gym:companyId:entity:action:...
                }
            }
            catch { }
            
            return "unknown";
        }

        #endregion
    }
}
