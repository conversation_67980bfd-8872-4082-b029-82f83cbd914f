using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserCompanyManager : IUserCompanyService
    {
        IUserCompanyDal _userCompanyDal;

        public UserCompanyManager(IUserCompanyDal userCompanyDal)
        {
            _userCompanyDal = userCompanyDal;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult Add(UserCompany userCompany)
        {
            // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı
            return _userCompanyDal.AddUserCompanyWithValidation(userCompany);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult Delete(int id)
        {
            _userCompanyDal.Delete(id);
            return new SuccessResult(Messages.UserCompanyDeleted);
        }
        [SecuredOperation("owner")]
        [CacheAspect(3600)] // 1 saat cache - Owner admin paneli için
        public IDataResult<List<UserCompany>> GetAll()
        {
            return new SuccessDataResult<List<UserCompany>>(_userCompanyDal.GetAll());
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(UserCompanyValidator))]
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult Update(UserCompany userCompany)
        {
            _userCompanyDal.Update(userCompany);
            return new SuccessResult(Messages.UserCompanyUpdated);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)] // 1 saat cache - Kullanıcı şirket detayları
        public IDataResult<List<UserCompanyDetailDto>> GetUserCompanyDetails()
        {
            return new SuccessDataResult<List<UserCompanyDetailDto>>(_userCompanyDal.GetUserCompanyDetails());
        }

        [PerformanceAspect(3)]
        // 🛡️ SECURITY: Cache YOK - Refresh token güvenliği için direkt DB'den çek
        // Multi-tenant sistemlerde company değişikliği anında yansımalı
        // ⚠️ CRITICAL: Bu metot kesinlikle cache'lenmemeli - güvenlik riski!
        public IDataResult<int> GetUserCompanyId(int userId)
        {
            int companyId = _userCompanyDal.GetUserCompanyId(userId);
            return new SuccessDataResult<int>(companyId);
        }

        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika cache - Kullanıcının şirketleri
        public IDataResult<List<UserCompany>> GetUserCompanies(int userId)
        {
            var userCompanies = _userCompanyDal.GetActiveUserCompanies(userId);
            if (userCompanies == null || userCompanies.Count == 0)
            {
                return new ErrorDataResult<List<UserCompany>>(Messages.UserCompanyNotFound);
            }
            return new SuccessDataResult<List<UserCompany>>(userCompanies);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        // ✅ Sadece UserCompany cache'ini temizle - GetUserCompanyId artık cache'siz
        [SmartCacheRemoveAspect("UserCompany")]
        public IResult UpdateActiveCompany(int userId, int companyId)
        {
            // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı
            return _userCompanyDal.UpdateActiveCompanyWithValidation(userId, companyId);
        }
    }
}
