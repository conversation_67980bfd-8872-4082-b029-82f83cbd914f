using Business.Abstract;
using Core.Utilities.Results;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Cache Owner Controller - SOLID prensiplerine uygun cache yönetimi
    /// Sadece Owner yetkisi gerektirir - SecuredOperation ile korunur
    /// Single Responsibility: Sadece HTTP endpoint'leri yönetir
    /// Dependency Inversion: ICacheOwnerService abstraction'ına bağımlı
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "owner")] // 🛡️ OWNER-ONLY ACCESS
    public class CacheOwnerController : ControllerBase
    {
        private readonly ICacheOwnerService _cacheOwnerService;

        public CacheOwnerController(ICacheOwnerService cacheOwnerService)
        {
            _cacheOwnerService = cacheOwnerService;
        }

        #region Company Cache Operations

        /// <summary>
        /// Cache istatistiklerini getirir (Company bazlı)
        /// </summary>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetCacheStatistics()
        {
            var result = await _cacheOwnerService.GetCacheStatisticsAsync();
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheStatisticsDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Cache detaylarını getirir (Company bazlı)
        /// </summary>
        [HttpGet("details")]
        public async Task<IActionResult> GetCacheDetails()
        {
            var result = await _cacheOwnerService.GetCacheDetailsAsync();
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheDetailsDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Company'nin tüm cache'ini temizler
        /// </summary>
        [HttpDelete("clear/tenant")]
        public async Task<IActionResult> ClearTenantCache()
        {
            var result = await _cacheOwnerService.ClearCompanyCacheAsync();
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheClearResultDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        #endregion

        #region Cache Key Operations

        /// <summary>
        /// Company'ye ait cache key'lerini listeler
        /// </summary>
        [HttpGet("keys")]
        public async Task<IActionResult> GetCompanyCacheKeys([FromQuery] int page = 1, [FromQuery] int size = 50)
        {
            var result = await _cacheOwnerService.GetCacheKeysAsync(page, size);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheKeysResponseDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Belirli pattern'e göre cache key'lerini listeler
        /// </summary>
        [HttpGet("keys/pattern/{pattern}")]
        public async Task<IActionResult> GetKeysByPattern(string pattern, [FromQuery] int page = 1, [FromQuery] int size = 50)
        {
            var result = await _cacheOwnerService.GetCacheKeysByPatternAsync(pattern, page, size);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheKeysResponseDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        [HttpDelete("key/{key}")]
        public async Task<IActionResult> DeleteCacheKey(string key)
        {
            var result = await _cacheOwnerService.DeleteCacheKeyAsync(key);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheKeyDeleteResultDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        [HttpGet("key/{key}/value")]
        public async Task<IActionResult> GetCacheKeyValue(string key)
        {
            var result = await _cacheOwnerService.GetCacheKeyValueAsync(key);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheKeyValueDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Belirli pattern'deki cache'leri temizler
        /// </summary>
        [HttpDelete("clear/pattern/{pattern}")]
        public async Task<IActionResult> ClearCacheByPattern(string pattern)
        {
            var result = await _cacheOwnerService.ClearCacheByPatternAsync(pattern);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheClearResultDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        #endregion

        #region Health & Monitoring

        /// <summary>
        /// Redis bağlantı durumunu kontrol eder
        /// </summary>
        [HttpGet("health")]
        public async Task<IActionResult> GetCacheHealth()
        {
            var result = await _cacheOwnerService.GetCacheHealthAsync();
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheHealthDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Real-time cache metrics getirir
        /// </summary>
        [HttpGet("metrics/realtime")]
        public async Task<IActionResult> GetRealtimeMetrics()
        {
            var result = await _cacheOwnerService.GetRealtimeMetricsAsync();
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheRealtimeMetricsDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        #endregion

        #region Cache Warmup

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        [HttpPost("warmup")]
        public async Task<IActionResult> WarmupCache([FromBody] CacheWarmupRequestDto request)
        {
            var result = await _cacheOwnerService.WarmupCacheAsync(request);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheWarmupResultDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        #endregion

        #region Multi-Company Operations (Owner Only)

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini getirir (Owner only)
        /// </summary>
        [HttpGet("multi-company/statistics")]
        public async Task<IActionResult> GetAllCompaniesStatistics()
        {
            var result = await _cacheOwnerService.GetAllCompaniesStatisticsAsync();
            
            if (result.Success)
                return Ok(new SuccessDataResult<MultiCompanyCacheStatisticsDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Belirli bir şirketin cache detaylarını getirir (Owner only)
        /// </summary>
        [HttpGet("multi-company/{companyId}/details")]
        public async Task<IActionResult> GetSpecificCompanyCacheDetails(int companyId)
        {
            var result = await _cacheOwnerService.GetSpecificCompanyCacheDetailsAsync(companyId);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CompanyCacheDetailsDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Belirli bir şirketin cache'ini temizler (Owner only)
        /// </summary>
        [HttpDelete("multi-company/{companyId}/clear")]
        public async Task<IActionResult> ClearSpecificCompanyCache(int companyId)
        {
            var result = await _cacheOwnerService.ClearSpecificCompanyCacheAsync(companyId);
            
            if (result.Success)
                return Ok(new SuccessDataResult<CacheClearResultDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        /// <summary>
        /// Çoklu şirket cache temizleme (Owner only)
        /// </summary>
        [HttpPost("multi-company/bulk-clear")]
        public async Task<IActionResult> BulkClearCompaniesCache([FromBody] BulkCacheOperationRequestDto request)
        {
            var result = await _cacheOwnerService.BulkClearCompaniesCache(request);
            
            if (result.Success)
                return Ok(new SuccessDataResult<BulkCacheClearResultDto>(result.Data, result.Message));
            
            return BadRequest(new ErrorResult(result.Message));
        }

        #endregion
    }
}
