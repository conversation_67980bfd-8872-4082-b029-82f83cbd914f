using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    #region Basic Cache DTOs

    /// <summary>
    /// Cache istatistikleri DTO
    /// </summary>
    public class CacheStatisticsDto
    {
        public int CompanyId { get; set; }
        public int TotalKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public double TotalMemoryUsageMB { get; set; }
        public Dictionary<string, int> KeysByEntity { get; set; } = new();
        public DateTime LastUpdated { get; set; }
        public List<CacheKeyDetailDto> TopKeys { get; set; } = new();
    }

    /// <summary>
    /// Cache detayları DTO
    /// </summary>
    public class CacheDetailsDto
    {
        public CacheStatisticsDto Statistics { get; set; }
        public CacheHealthDto Health { get; set; }
        public int CompanyId { get; set; }
        public string[] CachePatterns { get; set; }
    }

    /// <summary>
    /// Cache temizleme sonucu DTO
    /// </summary>
    public class CacheClearResultDto
    {
        public long RemovedCount { get; set; }
        public string Pattern { get; set; }
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public DateTime ClearedAt { get; set; }
    }

    /// <summary>
    /// Cache key listesi DTO
    /// </summary>
    public class CacheKeysResponseDto
    {
        public List<CacheKeyDetailDto> Keys { get; set; } = new();
        public CachePaginationDto Pagination { get; set; }
        public string Pattern { get; set; }
    }

    /// <summary>
    /// Cache key detayı DTO
    /// </summary>
    public class CacheKeyDetailDto
    {
        public string Key { get; set; }
        public string Type { get; set; }
        public long MemoryUsage { get; set; }
        public DateTime? ExpiryTime { get; set; }
        public TimeSpan? TimeToLive { get; set; }
        public string Entity { get; set; }
        public string Action { get; set; }
    }

    /// <summary>
    /// Cache pagination DTO
    /// </summary>
    public class CachePaginationDto
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    /// <summary>
    /// Cache key silme sonucu DTO
    /// </summary>
    public class CacheKeyDeleteResultDto
    {
        public string Key { get; set; }
        public bool Removed { get; set; }
        public DateTime DeletedAt { get; set; }
    }

    /// <summary>
    /// Cache key değeri DTO
    /// </summary>
    public class CacheKeyValueDto
    {
        public string Key { get; set; }
        public object Value { get; set; }
        public string Type { get; set; }
        public long MemoryUsage { get; set; }
        public DateTime? ExpiryTime { get; set; }
    }

    /// <summary>
    /// Cache sağlık durumu DTO
    /// </summary>
    public class CacheHealthDto
    {
        public bool IsConnected { get; set; }
        public double PingTime { get; set; }
        public double ResponseTime { get; set; }
        public string Status { get; set; }
        public object ServerInfo { get; set; }
    }

    /// <summary>
    /// Real-time cache metrics DTO
    /// </summary>
    public class CacheRealtimeMetricsDto
    {
        public int CompanyId { get; set; }
        public DateTime Timestamp { get; set; }
        public CacheStatisticsDto Statistics { get; set; }
        public CachePerformanceDto Performance { get; set; }
        public object ServerInfo { get; set; }
        public List<CacheKeyDetailDto> TopCacheKeys { get; set; } = new();
    }

    /// <summary>
    /// Cache performance DTO
    /// </summary>
    public class CachePerformanceDto
    {
        public double ResponseTime { get; set; }
        public bool IsConnected { get; set; }
        public int ConnectionCount { get; set; }
    }

    #endregion

    #region Cache Warmup DTOs

    /// <summary>
    /// Cache warmup request DTO
    /// </summary>
    public class CacheWarmupRequestDto
    {
        public bool WarmupMembers { get; set; } = true;
        public bool WarmupPayments { get; set; } = true;
        public bool WarmupMemberships { get; set; } = true;
        public bool WarmupUsers { get; set; } = false;
        public bool WarmupCompanySettings { get; set; } = false;
    }

    /// <summary>
    /// Cache warmup sonucu DTO
    /// </summary>
    public class CacheWarmupResultDto
    {
        public int CompanyId { get; set; }
        public int TotalWarmedKeys { get; set; }
        public Dictionary<string, int> WarmedKeysByEntity { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public DateTime CompletedAt { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    #endregion

    #region Multi-Company DTOs

    /// <summary>
    /// Multi-company cache istatistikleri DTO
    /// </summary>
    public class MultiCompanyCacheStatisticsDto
    {
        public int TotalCompanies { get; set; }
        public long TotalCacheKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public double AverageKeysPerCompany { get; set; }
        public List<CompanyCacheStatisticsDto> Companies { get; set; } = new();
    }

    /// <summary>
    /// Şirket cache istatistikleri DTO
    /// </summary>
    public class CompanyCacheStatisticsDto
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public CacheStatisticsDto Statistics { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreationDate { get; set; }
    }

    /// <summary>
    /// Şirket cache detayları DTO
    /// </summary>
    public class CompanyCacheDetailsDto
    {
        public CompanyDto Company { get; set; }
        public CacheDetailsDto CacheDetails { get; set; }
    }

    /// <summary>
    /// Toplu cache temizleme request DTO
    /// </summary>
    public class BulkCacheOperationRequestDto
    {
        public List<int> CompanyIds { get; set; } = new();
    }

    /// <summary>
    /// Toplu cache temizleme sonucu DTO
    /// </summary>
    public class BulkCacheClearResultDto
    {
        public long TotalRemovedCount { get; set; }
        public int ProcessedCompanies { get; set; }
        public List<BulkCacheOperationResultDto> Results { get; set; } = new();
    }

    /// <summary>
    /// Toplu cache operasyon sonucu DTO
    /// </summary>
    public class BulkCacheOperationResultDto
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public long RemovedCount { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }

    #endregion

    #region Helper DTOs

    /// <summary>
    /// Company DTO (cache operations için)
    /// </summary>
    public class CompanyDto
    {
        public int CompanyID { get; set; }
        public string CompanyName { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreationDate { get; set; }
    }

    #endregion
}
